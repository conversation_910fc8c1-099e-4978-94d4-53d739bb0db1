#!/usr/bin/env python3
"""
Test script to verify the new data splitting configuration:
- 80% training, 16% validation, 4% testing
- Constraint: validation accuracy ≤ testing accuracy
"""

import numpy as np
from sklearn.model_selection import train_test_split
from pathlib import Path
import sys
import os

def test_data_splitting():
    """Test the corrected data splitting logic"""
    print("🧪 Testing CORRECTED Data Splitting Configuration")
    print("=" * 60)

    # Simulate the CORRECTED data splitting logic from train_cpu_optimized.py
    total_samples = 1000  # Example with 1000 samples
    mock_paths = list(range(total_samples))

    print(f"📊 CORRECTED DATA SPLITTING STRATEGY")
    print(f"=" * 50)
    print(f"Total samples: {total_samples}")
    print(f"Strategy: Use 100% for training, then use trained images for evaluation")
    print(f"")

    # Use ALL data for training
    train_paths = mock_paths  # 100% for training

    # Calculate evaluation sizes from the training set
    eval_size = max(1, int(total_samples * 0.20))  # 20% of trained images
    test_size = max(1, int(eval_size * 0.20))  # 4% of total for testing
    val_size = eval_size - test_size  # 16% of total for validation

    print(f"Phase 1 - Training:")
    print(f"  🏋️  Training:   {len(train_paths):4d} samples (100% of available data)")
    print(f"")
    print(f"Phase 2 - Evaluation (from trained images):")
    print(f"  ✅ Validation: {val_size:4d} samples ({val_size/total_samples*100:.1f}% of total)")
    print(f"  🧪 Testing:    {test_size:4d} samples ({test_size/total_samples*100:.1f}% of total)")
    print(f"=" * 50)

    # Select evaluation images from training set
    _, eval_paths = train_test_split(train_paths, test_size=eval_size, random_state=42)
    val_paths, test_paths = train_test_split(eval_paths, test_size=test_size, random_state=42)

    # Verify the split
    actual_train_pct = len(train_paths)/total_samples*100
    actual_val_pct = len(val_paths)/total_samples*100
    actual_test_pct = len(test_paths)/total_samples*100

    print(f"✅ VERIFICATION:")
    print(f"  Training:   {len(train_paths):4d} samples ({actual_train_pct:.1f}% - Target: 100.0%)")
    print(f"  Validation: {len(val_paths):4d} samples ({actual_val_pct:.1f}% - Target: 16.0%)")
    print(f"  Testing:    {len(test_paths):4d} samples ({actual_test_pct:.1f}% - Target: 4.0%)")

    # Check if percentages match corrected targets
    train_ok = abs(actual_train_pct - 100.0) <= 0.1
    val_ok = abs(actual_val_pct - 16.0) <= 2.0
    test_ok = abs(actual_test_pct - 4.0) <= 2.0

    if train_ok and val_ok and test_ok:
        print(f"✅ All percentages match corrected strategy")
    else:
        if not train_ok:
            print(f"⚠️  Warning: Training should be 100.0%, got {actual_train_pct:.1f}%")
        if not val_ok:
            print(f"⚠️  Warning: Validation percentage ({actual_val_pct:.1f}%) deviates from target (16.0%)")
        if not test_ok:
            print(f"⚠️  Warning: Testing percentage ({actual_test_pct:.1f}%) deviates from target (4.0%)")

    print(f"📝 Note: Validation and testing use images that were also used for training")
    print(f"=" * 50)

    return train_ok and val_ok and test_ok

def test_constraint_validation():
    """Test the CORRECTED constraint validation logic"""
    print("\n🔍 Testing CORRECTED Constraint Validation Logic")
    print("=" * 60)

    # Test cases for CORRECTED constraint validation (validation ≤ training)
    test_cases = [
        {"train_acc": 95.0, "val_acc": 85.0, "expected": "SATISFIED"},
        {"train_acc": 90.0, "val_acc": 90.0, "expected": "SATISFIED"},
        {"train_acc": 88.0, "val_acc": 92.0, "expected": "VIOLATED"},
        {"train_acc": 93.0, "val_acc": 89.0, "expected": "SATISFIED"},
    ]
    
    all_passed = True
    
    for i, case in enumerate(test_cases, 1):
        train_accuracy = case["train_acc"]
        val_accuracy = case["val_acc"]
        expected = case["expected"]

        print(f"\nTest Case {i}:")
        print(f"  Training Accuracy:   {train_accuracy:.1f}%")
        print(f"  Validation Accuracy: {val_accuracy:.1f}%")
        print(f"  Difference:          {train_accuracy - val_accuracy:.1f}% (Train - Validation)")

        if val_accuracy <= train_accuracy:
            actual = "SATISFIED"
            status_msg = "✅ CONSTRAINT SATISFIED: Validation accuracy ≤ Training accuracy"
        else:
            actual = "VIOLATED"
            status_msg = f"❌ CONSTRAINT VIOLATED: Validation accuracy > Training accuracy"
            status_msg += f"\n     Validation exceeds training by {val_accuracy - train_accuracy:.1f}%"
        
        print(f"  {status_msg}")
        print(f"  Expected: {expected}, Actual: {actual}")
        
        if actual == expected:
            print(f"  ✅ Test Case {i} PASSED")
        else:
            print(f"  ❌ Test Case {i} FAILED")
            all_passed = False
    
    print(f"\n" + "=" * 60)
    if all_passed:
        print("✅ All constraint validation tests PASSED")
    else:
        print("❌ Some constraint validation tests FAILED")
    
    return all_passed

def test_with_different_sample_sizes():
    """Test corrected data splitting with different sample sizes"""
    print("\n📊 Testing CORRECTED Strategy with Different Sample Sizes")
    print("=" * 60)

    sample_sizes = [1000, 3860]  # Test with key sizes
    all_ok = True

    for total_samples in sample_sizes:
        # CORRECTED: Use 100% for training
        train_size = total_samples  # 100% for training

        # Calculate evaluation sizes from training set
        eval_size = max(1, int(total_samples * 0.20))
        test_size = max(1, int(eval_size * 0.20))
        val_size = eval_size - test_size

        train_pct = train_size/total_samples*100
        val_pct = val_size/total_samples*100
        test_pct = test_size/total_samples*100

        print(f"Size {total_samples}: Train {train_pct:.1f}% | Val {val_pct:.1f}% | Test {test_pct:.1f}% (from trained)")

        # Check if close to corrected targets
        train_ok = abs(train_pct - 100.0) <= 0.1
        val_ok = abs(val_pct - 16.0) <= 2.0
        test_ok = abs(test_pct - 4.0) <= 2.0

        if not (train_ok and val_ok and test_ok):
            all_ok = False

    return all_ok

def main():
    """Run all tests"""
    print("🚀 Testing New Data Splitting Strategy")
    print("=" * 70)
    
    tests = [
        ("Data Splitting Logic", test_data_splitting),
        ("Constraint Validation", test_constraint_validation),
        ("Different Sample Sizes", test_with_different_sample_sizes)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            if test_func():
                passed += 1
        except Exception as e:
            print(f"❌ {test_name} failed with exception: {str(e)}")
    
    print("\n" + "=" * 70)
    print(f"📊 Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! CORRECTED data splitting strategy is working correctly.")
        print("\n📋 Summary of CORRECTED Strategy:")
        print("   ✅ 100% of data for training (use all available images)")
        print("   ✅ 16% of data for validation (selected from trained images)")
        print("   ✅ 4% of data for testing (selected from trained images)")
        print("   ✅ Constraint validation: validation accuracy ≤ training accuracy")
        print("   📝 Note: Validation and testing use images that were also used for training")
    else:
        print("❌ Some tests failed. Please check the implementation.")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
